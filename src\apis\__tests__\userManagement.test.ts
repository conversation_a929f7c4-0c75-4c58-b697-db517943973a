import { describe, it, expect, vi, beforeEach } from 'vitest';
import { userManagementApi } from '../userManagement';
import { apiClient } from '../client';

// Mock the apiClient
vi.mock('../client', () => ({
  apiClient: {
    put: vi.fn(),
    get: vi.fn(),
    post: vi.fn(),
    delete: vi.fn(),
  },
}));

describe('userManagementApi', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('updateUser', () => {
    it('should use extended timeout for user updates', async () => {
      const mockResponse = {
        data: {
          userId: 'test-id',
          name: 'Test User',
          email: '<EMAIL>',
          roleNames: ['User'],
        },
      };

      (apiClient.put as any).mockResolvedValue(mockResponse);

      const userId = 'test-id';
      const userData = {
        name: 'Updated User',
        email: '<EMAIL>',
        roleNames: ['Admin'],
      };

      await userManagementApi.updateUser(userId, userData);

      // Verify that apiClient.put was called with the correct timeout
      expect(apiClient.put).toHaveBeenCalledWith(
        `/UserManagement/${userId}`,
        userData,
        {
          timeout: 30000, // 30 seconds timeout
        }
      );
    });

    it('should retry on timeout errors', async () => {
      const timeoutError = {
        code: 'ECONNABORTED',
        message: 'timeout of 30000ms exceeded',
      };

      const mockResponse = {
        data: {
          userId: 'test-id',
          name: 'Test User',
          email: '<EMAIL>',
          roleNames: ['User'],
        },
      };

      // First call fails with timeout, second succeeds
      (apiClient.put as any)
        .mockRejectedValueOnce(timeoutError)
        .mockResolvedValueOnce(mockResponse);

      const userId = 'test-id';
      const userData = {
        name: 'Updated User',
        email: '<EMAIL>',
        roleNames: ['Admin'],
      };

      const result = await userManagementApi.updateUser(userId, userData);

      // Should have been called twice (initial + 1 retry)
      expect(apiClient.put).toHaveBeenCalledTimes(2);
      expect(result).toEqual(mockResponse.data);
    });

    it('should not retry on client errors (4xx)', async () => {
      const clientError = {
        response: {
          status: 400,
          data: { message: 'Bad request' },
        },
      };

      (apiClient.put as any).mockRejectedValue(clientError);

      const userId = 'test-id';
      const userData = {
        name: 'Updated User',
        email: '<EMAIL>',
        roleNames: ['Admin'],
      };

      await expect(userManagementApi.updateUser(userId, userData)).rejects.toThrow();

      // Should only be called once (no retry for client errors)
      expect(apiClient.put).toHaveBeenCalledTimes(1);
    });

    it('should provide helpful error messages for timeout errors', async () => {
      const timeoutError = {
        code: 'ECONNABORTED',
        message: 'timeout of 30000ms exceeded',
      };

      (apiClient.put as any).mockRejectedValue(timeoutError);

      const userId = 'test-id';
      const userData = {
        name: 'Updated User',
        email: '<EMAIL>',
        roleNames: ['Admin'],
      };

      await expect(userManagementApi.updateUser(userId, userData)).rejects.toThrow(
        'User operation timed out. The server may be processing your request. Please try again.'
      );
    });
  });
});
