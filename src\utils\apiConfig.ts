/**
 * API Configuration utilities for managing timeouts and request settings
 */

export interface ApiTimeoutConfig {
  default: number;
  userOperations: number;
  fileUpload: number;
  dataFetch: number;
  authentication: number;
}

/**
 * Timeout configurations for different types of API operations
 */
export const API_TIMEOUTS: ApiTimeoutConfig = {
  default: 10000,        // 10 seconds - general API calls
  userOperations: 30000, // 30 seconds - user CRUD operations
  fileUpload: 60000,     // 60 seconds - file uploads
  dataFetch: 15000,      // 15 seconds - data fetching operations
  authentication: 20000, // 20 seconds - auth operations
};

/**
 * Get timeout for specific operation type
 */
export const getTimeoutForOperation = (operationType: keyof ApiTimeoutConfig): number => {
  return API_TIMEOUTS[operationType];
};

/**
 * Retry configuration for different operation types
 */
export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  shouldRetry: (error: any) => boolean;
}

export const RETRY_CONFIGS: Record<string, RetryConfig> = {
  userOperations: {
    maxRetries: 2,
    baseDelay: 1000,
    maxDelay: 5000,
    shouldRetry: (error: any) => {
      // Retry on timeout or server errors, but not client errors
      return error.code === 'ECONNABORTED' || 
             (error.response?.status >= 500 && error.response?.status < 600);
    },
  },
  dataFetch: {
    maxRetries: 3,
    baseDelay: 500,
    maxDelay: 3000,
    shouldRetry: (error: any) => {
      return error.code === 'ECONNABORTED' || 
             error.code === 'ERR_NETWORK' ||
             (error.response?.status >= 500 && error.response?.status < 600);
    },
  },
  default: {
    maxRetries: 1,
    baseDelay: 1000,
    maxDelay: 2000,
    shouldRetry: (error: any) => {
      return error.code === 'ECONNABORTED' || 
             (error.response?.status >= 500 && error.response?.status < 600);
    },
  },
};

/**
 * Get retry configuration for specific operation type
 */
export const getRetryConfig = (operationType: string): RetryConfig => {
  return RETRY_CONFIGS[operationType] || RETRY_CONFIGS.default;
};

/**
 * Enhanced error messages for different error types
 */
export const getErrorMessage = (error: any, operationType: string): string => {
  if (error.code === 'ECONNABORTED') {
    switch (operationType) {
      case 'userOperations':
        return 'User operation timed out. The server may be processing your request. Please try again.';
      case 'fileUpload':
        return 'File upload timed out. Please check your connection and try again.';
      case 'dataFetch':
        return 'Data fetch timed out. Please refresh the page or try again.';
      default:
        return 'Request timed out. Please try again.';
    }
  }
  
  if (error.code === 'ERR_NETWORK') {
    return 'Network error. Please check your internet connection and try again.';
  }
  
  if (error.response?.status === 401) {
    return 'Authentication failed. Please log in again.';
  }
  
  if (error.response?.status === 403) {
    return 'You do not have permission to perform this action.';
  }
  
  if (error.response?.status === 404) {
    return 'The requested resource was not found.';
  }
  
  if (error.response?.status >= 500) {
    return 'Server error. Please try again later.';
  }
  
  return error.response?.data?.message || error.message || 'An unexpected error occurred.';
};

/**
 * Utility function to create axios config with appropriate timeout
 */
export const createRequestConfig = (
  operationType: keyof ApiTimeoutConfig,
  additionalConfig: Record<string, any> = {}
) => {
  return {
    timeout: getTimeoutForOperation(operationType),
    ...additionalConfig,
  };
};
